<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res"><file name="ic_mute" path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\drawable\ic_mute.xml" qualifiers="" type="drawable"/><file name="activity_main" path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="player_tile" path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\layout\player_tile.xml" qualifiers="" type="layout"/><file name="cameras" path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\raw\cameras.json" qualifiers="" type="raw"/><file path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="android:Theme.DeviceDefault.NoActionBar">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@android:color/black</item>
    </style></file><file name="network_security_config" path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/><file path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\values\colors.xml" qualifiers=""><color name="focus_border_glow">#8000CCFF</color><color name="focus_border_medium">#FF33CCFF</color><color name="focus_border_main">#FF33DDFF</color><color name="focus_border_high_contrast">#FF0099CC</color></file><file name="focus_border_enhanced" path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\drawable\focus_border_enhanced.xml" qualifiers="" type="drawable"/><file name="focus_border_high_contrast" path="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\drawable\focus_border_high_contrast.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\link-eye\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\link-eye\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\link-eye\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\StudioProjects\link-eye\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>