<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.4.1" type="incidents">

    <incident
        id="MissingTvBanner"
        severity="error"
        message="Expecting `android:banner` with the `&lt;application>` tag or each Leanback launcher activity">
        <fix-attribute
            description="Set banner"
            namespace="http://schemas.android.com/apk/res/android"
            attribute="banner"
            value="TODO"
            point="4"
            mark="0"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="11"
            column="6"
            startOffset="555"
            endLine="11"
            endColumn="17"
            endOffset="566"/>
    </incident>

    <incident
        id="PermissionImpliesUnsupportedHardware"
        severity="warning"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.microphone&quot; required=&quot;false&quot;>` tag">
        <fix-data featureName="android.hardware.microphone"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="7"
            column="6"
            startOffset="302"
            endLine="7"
            endColumn="21"
            endOffset="317"/>
    </incident>

    <incident
        id="HardcodedText"
        severity="warning"
        message="Hardcoded string &quot;Cam&quot;, should use `@string` resource">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_tile.xml"
            line="32"
            column="4"
            startOffset="981"
            endLine="32"
            endColumn="22"
            endOffset="999"/>
    </incident>

    <incident
        id="ContentDescription"
        severity="warning"
        message="Missing `contentDescription` attribute on image">
        <fix-alternatives>
            <fix-attribute
                description="Set contentDescription"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="contentDescription"
                value="TODO"
                point="4"
                mark="0"/>
            <fix-attribute
                description="Set importantForAccessibility=&quot;no&quot;"
                namespace="http://schemas.android.com/apk/res/android"
                attribute="importantForAccessibility"
                value="no"/>
        </fix-alternatives>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_tile.xml"
            line="33"
            column="4"
            startOffset="1007"
            endLine="33"
            endColumn="13"
            endOffset="1016"/>
    </incident>

    <incident
        id="InsecureBaseConfiguration"
        severity="warning"
        message="Insecure Base Configuration">
        <fix-replace
            description="Replace with false"
            replacement="false"
            priority="0">
            <range
                file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
                startOffset="247"
                endOffset="251"/>
        </fix-replace>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
            line="6"
            column="45"
            startOffset="247"
            endLine="6"
            endColumn="49"
            endOffset="251"/>
    </incident>

    <incident
        id="AcceptsUserCertificates"
        severity="warning"
        message="The Network Security Configuration allows the use of user certificates in the release version of your app">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml"
            line="9"
            column="13"
            startOffset="331"
            endLine="9"
            endColumn="39"
            endOffset="357"/>
    </incident>

    <incident
        id="DiscouragedApi"
        severity="warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`).">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/linkeye/CameraRepository.java"
            line="20"
            column="25"
            startOffset="510"
            endLine="20"
            endColumn="38"
            endOffset="523"/>
    </incident>

    <incident
        id="CustomX509TrustManager"
        severity="warning"
        message="Implementing a custom `X509TrustManager` is error-prone and likely to be insecure. It is likely to disable certificate validation altogether, and is non-trivial to implement correctly without calling Android&apos;s default implementation.">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/linkeye/M3U8Parser.java"
            line="165"
            column="21"
            startOffset="6597"
            endLine="165"
            endColumn="37"
            endOffset="6613"/>
    </incident>

    <incident
        id="TrustAllX509TrustManager"
        severity="warning"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/linkeye/M3U8Parser.java"
            line="170"
            column="33"
            startOffset="6891"
            endLine="170"
            endColumn="51"
            endOffset="6909"/>
    </incident>

    <incident
        id="TrustAllX509TrustManager"
        severity="warning"
        message="`checkClientTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/linkeye/M3U8Parser.java"
            line="169"
            column="33"
            startOffset="6795"
            endLine="169"
            endColumn="51"
            endOffset="6813"/>
    </incident>

    <incident
        id="BadHostnameVerifier"
        severity="warning"
        message="`verify` always returns `true`, which could cause insecure network traffic due to trusting TLS/SSL server certificates for wrong hostnames">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/linkeye/M3U8Parser.java"
            line="181"
            column="32"
            startOffset="7401"
            endLine="181"
            endColumn="38"
            endOffset="7407"/>
    </incident>

    <incident
        id="ObsoleteSdkInt"
        severity="warning"
        message="Unnecessary; SDK_INT is always >= 23">
        <fix-data conditional="true"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*javaDir*0}/com/example/linkeye/MainActivity.java"
            line="934"
            column="13"
            startOffset="35076"
            endLine="934"
            endColumn="64"
            endOffset="35127"/>
    </incident>

    <incident
        id="OldTargetApi"
        severity="warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details.">
        <fix-replace
            description="Update targetSdkVersion to 36"
            oldString="35"
            replacement="36"
            priority="0"/>
        <location
            file="${:app*projectDir}/build.gradle"
            line="13"
            column="9"
            startOffset="265"
            endLine="13"
            endColumn="28"
            endOffset="284"/>
    </incident>

    <incident
        id="MergeRootFrame"
        severity="warning"
        message="This `&lt;FrameLayout>` can be replaced with a `&lt;merge>` tag">
        <location
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml"
            line="2"
            column="1"
            startOffset="40"
            endLine="17"
            endColumn="15"
            endOffset="610"/>
    </incident>

</incidents>
