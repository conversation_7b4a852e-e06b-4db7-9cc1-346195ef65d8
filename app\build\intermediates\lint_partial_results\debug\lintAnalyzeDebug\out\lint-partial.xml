<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.4.1" type="partial_results">
    <map id="UnsafeIntentLaunch">
            <map id="unprotected">
                <entry
                    name="com.example.linkeye.MainActivity"
                    boolean="true"/>
            </map>
    </map>
    <map id="UnusedResources">
        <location id="R.color.focus_border_high_contrast"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="14"
            column="12"
            startOffset="577"
            endLine="14"
            endColumn="45"
            endOffset="610"/>
        <location id="R.color.focus_border_medium"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml"
            line="17"
            column="12"
            startOffset="704"
            endLine="17"
            endColumn="38"
            endOffset="730"/>
        <entry
            name="model"
            string="color[focus_border_glow(U),focus_border_main(U),focus_border_high_contrast(D),focus_border_medium(D)],drawable[focus_border_enhanced(U),focus_border_high_contrast(D),ic_mute(U)],id[grid_container(U),tile_root(D),surface_view(U),title(U),mute_badge(U),focus_border(U),loading_indicator(U)],layout[activity_main(U),player_tile(U)],raw[cameras(D)],style[AppTheme(U)],xml[network_security_config(U)];4^0^1,5^2,f^6;;;"/>
        <location id="R.drawable.focus_border_high_contrast"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/focus_border_high_contrast.xml"
            line="2"
            column="1"
            startOffset="39"
            endLine="30"
            endColumn="14"
            endOffset="959"/>
        <location id="R.raw.cameras"
            file="${:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/cameras.json"/>
    </map>

</incidents>
