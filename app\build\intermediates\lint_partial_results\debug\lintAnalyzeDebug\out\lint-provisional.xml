<?xml version="1.0" encoding="UTF-8"?>
<incidents format="6" by="lint 8.4.1" type="conditional_incidents">

    <incident
        id="UnusedAttribute"
        severity="warning"
        message="">
        <fix-data minSdk="ffffffffffc00000" requiresApi="ffffffffff800000"/>
        <location
            file="${:app*debug*MAIN*sourceProvider*0*manifest*0}"
            line="15"
            column="9"
            startOffset="690"
            endLine="15"
            endColumn="69"
            endOffset="750"/>
        <map>
            <api-levels id="minSdk"
                value="ffffffffffc00000"/>
            <api-levels id="requiresApi"
                value="ffffffffff800000"/>
            <entry
                name="message"
                string="Attribute `networkSecurityConfig` is only used in API level 24 and higher (current min is %1$s)"/>
        </map>
    </incident>

</incidents>
