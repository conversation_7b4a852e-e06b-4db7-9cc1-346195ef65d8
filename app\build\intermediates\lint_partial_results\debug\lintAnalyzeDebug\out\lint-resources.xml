http://schemas.android.com/apk/res-auto;;${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/colors.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/ic_mute.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/focus_border_high_contrast.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/drawable/focus_border_enhanced.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/activity_main.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/layout/player_tile.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/raw/cameras.json,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/values/styles.xml,${\:app*debug*MAIN*sourceProvider*0*resDir*0}/xml/network_security_config.xml,+color:focus_border_glow,0,V400090185,35000901b6,;"#8000CCFF";focus_border_high_contrast,0,V4000d023a,3e000d0274,;"#FF0099CC";focus_border_medium,0,V4001002b9,37001002ec,;"#FF33CCFF";focus_border_main,0,V400060112,**********,;"#FF33DDFF";+drawable:ic_mute,1,F;focus_border_high_contrast,2,F;focus_border_enhanced,3,F;+id:grid_container,4,F;focus_border,5,F;mute_badge,5,F;surface_view,5,F;loading_indicator,5,F;tile_root,5,F;title,5,F;+layout:activity_main,4,F;player_tile,5,F;+raw:cameras,6,F;+style:AppTheme,7,V40003005c,c000901e5,;Dandroid\:Theme.DeviceDefault.NoActionBar,android\:windowNoTitle:true,android\:windowActionBar:false,android\:windowFullscreen:true,android\:windowContentOverlay:@null,android\:windowBackground:@android\:color/black,;+xml:network_security_config,8,F;