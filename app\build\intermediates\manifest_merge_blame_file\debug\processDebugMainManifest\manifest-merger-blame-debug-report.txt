1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.linkeye.debug" >
4
5    <uses-sdk
6        android:minSdkVersion="23"
7        android:targetSdkVersion="35" />
8
9    <uses-permission android:name="android.permission.INTERNET" />
9-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:5-67
9-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:5:22-64
10    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
10-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:5-80
10-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:6:22-77
11    <uses-permission android:name="android.permission.RECORD_AUDIO" />
11-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:5-71
11-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7:22-68
12
13    <uses-feature
13-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:8:5-87
14        android:name="android.software.leanback"
14-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:8:19-59
15        android:required="false" />
15-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:8:60-84
16    <uses-feature
16-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:5-90
17        android:name="android.hardware.touchscreen"
17-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:19-62
18        android:required="false" />
18-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:9:63-87
19
20    <application
20-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:11:5-28:19
21        android:allowBackup="true"
21-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:13:9-35
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:hardwareAccelerated="true"
24-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:14:9-43
25        android:label="link-eye"
25-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:12:9-33
26        android:networkSecurityConfig="@xml/network_security_config"
26-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:15:9-69
27        android:testOnly="true"
28        android:theme="@style/AppTheme"
28-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:17:9-40
29        android:usesCleartextTraffic="true" >
29-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:16:9-44
30        <activity
30-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:18:9-27:20
31            android:name="com.example.linkeye.MainActivity"
31-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:19:13-41
32            android:configChanges="keyboardHidden|orientation|screenSize"
32-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:21:13-74
33            android:exported="true" >
33-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:20:13-36
34            <intent-filter>
34-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:22:13-26:29
35                <action android:name="android.intent.action.MAIN" />
35-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:23:17-69
35-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:23:25-66
36
37                <category android:name="android.intent.category.LAUNCHER" />
37-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:24:17-77
37-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:24:27-74
38                <category android:name="android.intent.category.LEANBACK_LAUNCHER" />
38-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:25:17-86
38-->C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:25:27-83
39            </intent-filter>
40        </activity>
41    </application>
42
43</manifest>
