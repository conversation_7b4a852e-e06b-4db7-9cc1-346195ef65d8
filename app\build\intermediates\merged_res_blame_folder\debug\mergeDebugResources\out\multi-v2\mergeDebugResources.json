{"logs": [{"outputFile": "com.example.linkeye.app-debug-4:/values_values.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\StudioProjects\\link-eye\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "9,-1,6,-1", "startColumns": "4,-1,4,-1", "startOffsets": "389,-1,274,-1", "endColumns": "53,-1,53,-1", "endOffsets": "438,-1,323,-1"}, "to": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,109,172,226", "endColumns": "53,62,53,55", "endOffsets": "104,167,221,277"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\link-eye\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "6", "startColumns": "4", "startOffsets": "282", "endLines": "12", "endColumns": "12", "endOffsets": "675"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\9.0-milestone-1\\com.example.linkeye.app-debug-4:\\values_values.arsc.flat", "map": [{"source": "C:\\Users\\<USER>\\StudioProjects\\link-eye\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "92", "endLines": "9", "endColumns": "12", "endOffsets": "485"}, "to": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "8", "endColumns": "12", "endOffsets": "448"}}]}]}