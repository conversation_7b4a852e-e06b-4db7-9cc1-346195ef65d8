{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\9.0-milestone-1\\com.example.linkeye.app-mergeDebugResources-2:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\StudioProjects\\link-eye\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "8,12,5,15", "startColumns": "4,4,4,4", "startOffsets": "386,567,228,694", "endColumns": "53,62,53,55", "endOffsets": "435,625,277,745"}, "to": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,109,172,226", "endColumns": "53,62,53,55", "endOffsets": "104,167,221,277"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\link-eye\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "6", "startColumns": "4", "startOffsets": "282", "endLines": "12", "endColumns": "12", "endOffsets": "675"}}]}, {"outputFile": "com.example.linkeye.app-mergeDebugResources-2:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\StudioProjects\\link-eye\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "9,-1,6,-1", "startColumns": "4,-1,4,-1", "startOffsets": "389,-1,274,-1", "endColumns": "53,-1,53,-1", "endOffsets": "438,-1,323,-1"}, "to": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,109,172,226", "endColumns": "53,62,53,55", "endOffsets": "104,167,221,277"}}, {"source": "C:\\Users\\<USER>\\StudioProjects\\link-eye\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "6", "startColumns": "4", "startOffsets": "282", "endLines": "12", "endColumns": "12", "endOffsets": "675"}}]}]}