C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:11: Error: Expecting android:banner with the <application> tag or each Leanback launcher activity [MissingTvBanner]
    <application
     ~~~~~~~~~~~

   Explanation for issues of type "MissingTvBanner":
   A TV application must provide a home screen banner for each localization if
   it includes a Leanback launcher intent filter. The banner is the app launch
   point that appears on the home screen in the apps and games rows.

   https://developer.android.com/training/tv/start/start.html#banner

C:\Users\<USER>\StudioProjects\link-eye\app\build.gradle:13: Warning: Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the android.os.Build.VERSION_CODES javadoc for details. [OldTargetApi]
        targetSdkVersion 35
        ~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "OldTargetApi":
   When your application runs on a version of Android that is more recent than
   your targetSdkVersion specifies that it has been tested with, various
   compatibility modes kick in. This ensures that your application continues
   to work, but it may look out of place. For example, if the targetSdkVersion
   is less than 14, your app may get an option button in the UI.

   To fix this issue, set the targetSdkVersion to the highest available value.
   Then test your app to make sure everything works correctly. You may want to
   consult the compatibility notes to see what changes apply to each version
   you are adding support for:
   https://developer.android.com/reference/android/os/Build.VERSION_CODES.html
   as well as follow this guide:
   https://developer.android.com/distribute/best-practices/develop/target-sdk.
   html

   https://developer.android.com/distribute/best-practices/develop/target-sdk.html

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:15: Warning: Attribute networkSecurityConfig is only used in API level 24 and higher (current min is 23) [UnusedAttribute]
        android:networkSecurityConfig="@xml/network_security_config"
        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "UnusedAttribute":
   This check finds attributes set in XML files that were introduced in a
   version newer than the oldest version targeted by your application (with
   the minSdkVersion attribute).

   This is not an error; the application will simply ignore the attribute.
   However, if the attribute is important to the appearance or functionality
   of your application, you should consider finding an alternative way to
   achieve the same result with only available attributes, and then you can
   optionally create a copy of the layout in a layout-vNN folder which will be
   used on API NN or higher where you can take advantage of the newer
   attribute.

   Note: This check does not only apply to attributes. For example, some tags
   can be unused too, such as the new <tag> element in layouts introduced in
   API 21.

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:7: Warning: Permission exists without corresponding hardware <uses-feature android:name="android.hardware.microphone" required="false"> tag [PermissionImpliesUnsupportedHardware]
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
     ~~~~~~~~~~~~~~~

   Explanation for issues of type "PermissionImpliesUnsupportedHardware":
   The <uses-permission> element should not require a permission that implies
   an unsupported TV hardware feature. Google Play assumes that certain
   hardware related permissions indicate that the underlying hardware features
   are required by default. To fix the issue, consider declaring the
   corresponding uses-feature element with required="false" attribute.

   https://developer.android.com/guide/topics/manifest/uses-feature-element.html#permissions

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\CameraRepository.java:20: Warning: Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. R.foo.bar) than by name (e.g. getIdentifier("bar", "foo", null)). [DiscouragedApi]
   int resourceId = res.getIdentifier("cameras", "raw", context.getPackageName());
                        ~~~~~~~~~~~~~

   Explanation for issues of type "DiscouragedApi":
   Discouraged APIs are allowed and are not deprecated, but they may be unfit
   for common use (e.g. due to slow performance or subtle behavior).

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\M3U8Parser.java:181: Warning: verify always returns true, which could cause insecure network traffic due to trusting TLS/SSL server certificates for wrong hostnames [BadHostnameVerifier]
                public boolean verify(String hostname, SSLSession session) {
                               ~~~~~~

   Explanation for issues of type "BadHostnameVerifier":
   This check looks for implementations of HostnameVerifier whose verify
   method always returns true (thus trusting any hostname) which could result
   in insecure network traffic caused by trusting arbitrary hostnames in
   TLS/SSL certificates presented by peers.

   https://goo.gle/BadHostnameVerifier

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\M3U8Parser.java:169: Warning: checkClientTrusted is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers [TrustAllX509TrustManager]
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                                ~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\M3U8Parser.java:170: Warning: checkServerTrusted is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers [TrustAllX509TrustManager]
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                                ~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "TrustAllX509TrustManager":
   This check looks for X509TrustManager implementations whose
   checkServerTrusted or checkClientTrusted methods do nothing (thus trusting
   any certificate chain) which could result in insecure network traffic
   caused by trusting arbitrary TLS/SSL certificates presented by peers.

   https://goo.gle/TrustAllX509TrustManager

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\xml\network_security_config.xml:9: Warning: The Network Security Configuration allows the use of user certificates in the release version of your app [AcceptsUserCertificates]
            <certificates src="user"/>
            ~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "AcceptsUserCertificates":
   Allowing user certificates could allow eavesdroppers to intercept data sent
   by your app, 'which could impact the privacy of your users. Consider
   nesting your app's trust-anchors inside a <debug-overrides> element to make
   sure they are only available when android:debuggable is set to "true".

   https://goo.gle/AcceptsUserCertificates
   https://developer.android.com/training/articles/security-config#TrustingDebugCa

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\M3U8Parser.java:165: Warning: Implementing a custom X509TrustManager is error-prone and likely to be insecure. It is likely to disable certificate validation altogether, and is non-trivial to implement correctly without calling Android's default implementation. [CustomX509TrustManager]
                new X509TrustManager() {
                    ~~~~~~~~~~~~~~~~

   Explanation for issues of type "CustomX509TrustManager":
   This check looks for custom X509TrustManager implementations.

   https://goo.gle/CustomX509TrustManager

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\xml\network_security_config.xml:6: Warning: Insecure Base Configuration [InsecureBaseConfiguration]
    <base-config cleartextTrafficPermitted="true">
                                            ~~~~

   Explanation for issues of type "InsecureBaseConfiguration":
   Permitting cleartext traffic could allow eavesdroppers to intercept data
   sent by your app, which impacts the privacy of your users. Consider only
   allowing encrypted traffic by setting the cleartextTrafficPermitted tag to
   "false".

   https://goo.gle/InsecureBaseConfiguration
   https://developer.android.com/preview/features/security-config.html

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\MainActivity.java:934: Warning: Unnecessary; SDK_INT is always >= 23 [ObsoleteSdkInt]
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "ObsoleteSdkInt":
   This check flags version checks that are not necessary, because the
   minSdkVersion (or surrounding known API level) is already at least as high
   as the version checked for.

   Similarly, it also looks for resources in -vNN folders, such as values-v14
   where the version qualifier is less than or equal to the minSdkVersion,
   where the contents should be merged into the best folder.

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\layout\activity_main.xml:2: Warning: This <FrameLayout> can be replaced with a <merge> tag [MergeRootFrame]
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
^

   Explanation for issues of type "MergeRootFrame":
   If a <FrameLayout> is the root of a layout and does not provide background
   or padding etc, it can often be replaced with a <merge> tag which is
   slightly more efficient. Note that this depends on context, so make sure
   you understand how the <merge> tag works before proceeding.

   https://android-developers.googleblog.com/2009/03/android-layout-tricks-3-optimize-by.html

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\raw\cameras.json: Warning: The resource R.raw.cameras appears to be unused [UnusedResources]
C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\values\colors.xml:14: Warning: The resource R.color.focus_border_high_contrast appears to be unused [UnusedResources]
    <color name="focus_border_high_contrast">#FF0099CC</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\values\colors.xml:17: Warning: The resource R.color.focus_border_medium appears to be unused [UnusedResources]
    <color name="focus_border_medium">#FF33CCFF</color>
           ~~~~~~~~~~~~~~~~~~~~~~~~~~
C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\drawable\focus_border_high_contrast.xml:2: Warning: The resource R.drawable.focus_border_high_contrast appears to be unused [UnusedResources]
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
^

   Explanation for issues of type "UnusedResources":
   Unused resources make applications larger and slow down builds.


   The unused resource check can ignore tests. If you want to include
   resources that are only referenced from tests, consider packaging them in a
   test source set instead.

   You can include test sources in the unused resource check by setting the
   system property lint.unused-resources.include-tests =true, and to exclude
   them (usually for performance reasons), use
   lint.unused-resources.exclude-tests =true.
   ,

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml:11: Warning: Should explicitly set android:icon, there is no default [MissingApplicationIcon]
    <application
     ~~~~~~~~~~~

   Explanation for issues of type "MissingApplicationIcon":
   You should set an icon for the application as whole because there is no
   default. This attribute must be set as a reference to a drawable resource
   containing the image (for example @drawable/icon).

   https://developer.android.com/studio/publish/preparing#publishing-configure

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\layout\player_tile.xml:33: Warning: Missing contentDescription attribute on image [ContentDescription]
  <ImageView
   ~~~~~~~~~

   Explanation for issues of type "ContentDescription":
   Non-textual widgets like ImageViews and ImageButtons should use the
   contentDescription attribute to specify a textual description of the widget
   such that screen readers and other accessibility tools can adequately
   describe the user interface.

   Note that elements in application screens that are purely decorative and do
   not provide any content or enable a user action should not have
   accessibility content descriptions. In this case, set their descriptions to
   @null. If your app's minSdkVersion is 16 or higher, you can instead set
   these graphical elements' android:importantForAccessibility attributes to
   no.

   Note that for text fields, you should not set both the hint and the
   contentDescription attributes since the hint will never be shown. Just set
   the hint.

   https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases

C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\layout\player_tile.xml:32: Warning: Hardcoded string "Cam", should use @string resource [HardcodedText]
   android:text="Cam" />
   ~~~~~~~~~~~~~~~~~~

   Explanation for issues of type "HardcodedText":
   Hardcoding text attributes directly in layout files is bad for several
   reasons:

   * When creating configuration variations (for example for landscape or
   portrait) you have to repeat the actual text (and keep it up to date when
   making changes)

   * The application cannot be translated to other languages by just adding
   new translations for existing string resources.

   There are quickfixes to automatically extract this hardcoded string into a
   resource lookup.

1 errors, 19 warnings
