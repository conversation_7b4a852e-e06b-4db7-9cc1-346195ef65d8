<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.4.1">

    <issue
        id="MissingTvBanner"
        severity="Error"
        message="Expecting `android:banner` with the `&lt;application>` tag or each Leanback launcher activity"
        category="Correctness"
        priority="7"
        summary="TV Missing Banner"
        explanation="A TV application must provide a home screen banner for each localization if it includes a Leanback launcher intent filter. The banner is the app launch point that appears on the home screen in the apps and games rows."
        url="https://developer.android.com/training/tv/start/start.html#banner"
        urls="https://developer.android.com/training/tv/start/start.html#banner"
        errorLine1="    &lt;application"
        errorLine2="     ~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml"
            line="11"
            column="6"/>
    </issue>

    <issue
        id="OldTargetApi"
        severity="Warning"
        message="Not targeting the latest versions of Android; compatibility modes apply. Consider testing and updating this version. Consult the `android.os.Build.VERSION_CODES` javadoc for details."
        category="Correctness"
        priority="6"
        summary="Target SDK attribute is not targeting latest version"
        explanation="When your application runs on a version of Android that is more recent than your `targetSdkVersion` specifies that it has been tested with, various compatibility modes kick in. This ensures that your application continues to work, but it may look out of place. For example, if the `targetSdkVersion` is less than 14, your app may get an option button in the UI.&#xA;&#xA;To fix this issue, set the `targetSdkVersion` to the highest available value. Then test your app to make sure everything works correctly. You may want to consult the compatibility notes to see what changes apply to each version you are adding support for: https://developer.android.com/reference/android/os/Build.VERSION_CODES.html as well as follow this guide:&#xA;https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        url="https://developer.android.com/distribute/best-practices/develop/target-sdk.html"
        urls="https://developer.android.com/distribute/best-practices/develop/target-sdk.html,https://developer.android.com/reference/android/os/Build.VERSION_CODES.html"
        errorLine1="        targetSdkVersion 35"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\build.gradle"
            line="13"
            column="9"/>
    </issue>

    <issue
        id="UnusedAttribute"
        severity="Warning"
        message="Attribute `networkSecurityConfig` is only used in API level 24 and higher (current min is 23)"
        category="Correctness"
        priority="6"
        summary="Attribute unused on older versions"
        explanation="This check finds attributes set in XML files that were introduced in a version newer than the oldest version targeted by your application (with the `minSdkVersion` attribute).&#xA;&#xA;This is not an error; the application will simply ignore the attribute. However, if the attribute is important to the appearance or functionality of your application, you should consider finding an alternative way to achieve the same result with only available attributes, and then you can optionally create a copy of the layout in a layout-vNN folder which will be used on API NN or higher where you can take advantage of the newer attribute.&#xA;&#xA;Note: This check does not only apply to attributes. For example, some tags can be unused too, such as the new `&lt;tag>` element in layouts introduced in API 21."
        errorLine1="        android:networkSecurityConfig=&quot;@xml/network_security_config&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml"
            line="15"
            column="9"/>
    </issue>

    <issue
        id="PermissionImpliesUnsupportedHardware"
        severity="Warning"
        message="Permission exists without corresponding hardware `&lt;uses-feature android:name=&quot;android.hardware.microphone&quot; required=&quot;false&quot;>` tag"
        category="Correctness"
        priority="3"
        summary="Permission Implies Unsupported Hardware"
        explanation="The `&lt;uses-permission>` element should not require a permission that implies an unsupported TV hardware feature. Google Play assumes that certain hardware related permissions indicate that the underlying hardware features are required by default. To fix the issue, consider declaring the corresponding `uses-feature` element with `required=&quot;false&quot;` attribute."
        url="https://developer.android.com/guide/topics/manifest/uses-feature-element.html#permissions"
        urls="https://developer.android.com/guide/topics/manifest/uses-feature-element.html#permissions"
        errorLine1="    &lt;uses-permission android:name=&quot;android.permission.RECORD_AUDIO&quot; />"
        errorLine2="     ~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml"
            line="7"
            column="6"/>
    </issue>

    <issue
        id="DiscouragedApi"
        severity="Warning"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`)."
        category="Correctness"
        priority="2"
        summary="Using discouraged APIs"
        explanation="Discouraged APIs are allowed and are not deprecated, but they may be unfit for common use (e.g. due to slow performance or subtle behavior)."
        errorLine1="   int resourceId = res.getIdentifier(&quot;cameras&quot;, &quot;raw&quot;, context.getPackageName());"
        errorLine2="                        ~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\CameraRepository.java"
            line="20"
            column="25"/>
    </issue>

    <issue
        id="BadHostnameVerifier"
        severity="Warning"
        message="`verify` always returns `true`, which could cause insecure network traffic due to trusting TLS/SSL server certificates for wrong hostnames"
        category="Security"
        priority="6"
        summary="Insecure HostnameVerifier"
        explanation="This check looks for implementations of `HostnameVerifier` whose `verify` method always returns true (thus trusting any hostname) which could result in insecure network traffic caused by trusting arbitrary hostnames in TLS/SSL certificates presented by peers."
        url="https://goo.gle/BadHostnameVerifier"
        urls="https://goo.gle/BadHostnameVerifier"
        errorLine1="                public boolean verify(String hostname, SSLSession session) {"
        errorLine2="                               ~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\M3U8Parser.java"
            line="181"
            column="32"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        severity="Warning"
        message="`checkClientTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers"
        category="Security"
        priority="6"
        summary="Insecure TLS/SSL trust manager"
        explanation="This check looks for X509TrustManager implementations whose `checkServerTrusted` or `checkClientTrusted` methods do nothing (thus trusting any certificate chain) which could result in insecure network traffic caused by trusting arbitrary TLS/SSL certificates presented by peers."
        url="https://goo.gle/TrustAllX509TrustManager"
        urls="https://goo.gle/TrustAllX509TrustManager"
        errorLine1="                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\M3U8Parser.java"
            line="169"
            column="33"/>
    </issue>

    <issue
        id="TrustAllX509TrustManager"
        severity="Warning"
        message="`checkServerTrusted` is empty, which could cause insecure network traffic due to trusting arbitrary TLS/SSL certificates presented by peers"
        category="Security"
        priority="6"
        summary="Insecure TLS/SSL trust manager"
        explanation="This check looks for X509TrustManager implementations whose `checkServerTrusted` or `checkClientTrusted` methods do nothing (thus trusting any certificate chain) which could result in insecure network traffic caused by trusting arbitrary TLS/SSL certificates presented by peers."
        url="https://goo.gle/TrustAllX509TrustManager"
        urls="https://goo.gle/TrustAllX509TrustManager"
        errorLine1="                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\M3U8Parser.java"
            line="170"
            column="33"/>
    </issue>

    <issue
        id="AcceptsUserCertificates"
        severity="Warning"
        message="The Network Security Configuration allows the use of user certificates in the release version of your app"
        category="Security"
        priority="5"
        summary="Allowing User Certificates"
        explanation="Allowing user certificates could allow eavesdroppers to intercept data sent by your app, &apos;which could impact the privacy of your users. Consider nesting your app&apos;s `trust-anchors` inside a `&lt;debug-overrides>` element to make sure they are only available when `android:debuggable` is set to `&quot;true&quot;`."
        url="https://goo.gle/AcceptsUserCertificates"
        urls="https://goo.gle/AcceptsUserCertificates,https://developer.android.com/training/articles/security-config#TrustingDebugCa"
        errorLine1="            &lt;certificates src=&quot;user&quot;/>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\xml\network_security_config.xml"
            line="9"
            column="13"/>
    </issue>

    <issue
        id="CustomX509TrustManager"
        severity="Warning"
        message="Implementing a custom `X509TrustManager` is error-prone and likely to be insecure. It is likely to disable certificate validation altogether, and is non-trivial to implement correctly without calling Android&apos;s default implementation."
        category="Security"
        priority="5"
        summary="Implements custom TLS trust manager"
        explanation="This check looks for custom `X509TrustManager` implementations."
        url="https://goo.gle/CustomX509TrustManager"
        urls="https://goo.gle/CustomX509TrustManager"
        errorLine1="                new X509TrustManager() {"
        errorLine2="                    ~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\M3U8Parser.java"
            line="165"
            column="21"/>
    </issue>

    <issue
        id="InsecureBaseConfiguration"
        severity="Warning"
        message="Insecure Base Configuration"
        category="Security"
        priority="5"
        summary="Insecure Base Configuration"
        explanation="Permitting cleartext traffic could allow eavesdroppers to intercept data sent by your app, which impacts the privacy of your users. Consider only allowing encrypted traffic by setting the `cleartextTrafficPermitted` tag to `&quot;false&quot;`."
        url="https://goo.gle/InsecureBaseConfiguration"
        urls="https://goo.gle/InsecureBaseConfiguration,https://developer.android.com/preview/features/security-config.html"
        errorLine1="    &lt;base-config cleartextTrafficPermitted=&quot;true&quot;>"
        errorLine2="                                            ~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\xml\network_security_config.xml"
            line="6"
            column="45"/>
    </issue>

    <issue
        id="ObsoleteSdkInt"
        severity="Warning"
        message="Unnecessary; SDK_INT is always >= 23"
        category="Performance"
        priority="6"
        summary="Obsolete SDK_INT Version Check"
        explanation="This check flags version checks that are not necessary, because the `minSdkVersion` (or surrounding known API level) is already at least as high as the version checked for.&#xA;&#xA;Similarly, it also looks for resources in `-vNN` folders, such as `values-v14` where the version qualifier is less than or equal to the `minSdkVersion`, where the contents should be merged into the best folder."
        errorLine1="        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\java\com\example\linkeye\MainActivity.java"
            line="934"
            column="13"/>
    </issue>

    <issue
        id="MergeRootFrame"
        severity="Warning"
        message="This `&lt;FrameLayout>` can be replaced with a `&lt;merge>` tag"
        category="Performance"
        priority="4"
        summary="FrameLayout can be replaced with `&lt;merge>` tag"
        explanation="If a `&lt;FrameLayout>` is the root of a layout and does not provide background or padding etc, it can often be replaced with a `&lt;merge>` tag which is slightly more efficient. Note that this depends on context, so make sure you understand how the `&lt;merge>` tag works before proceeding."
        url="https://android-developers.googleblog.com/2009/03/android-layout-tricks-3-optimize-by.html"
        urls="https://android-developers.googleblog.com/2009/03/android-layout-tricks-3-optimize-by.html"
        errorLine1="&lt;FrameLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\layout\activity_main.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.raw.cameras` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\raw\cameras.json"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.focus_border_high_contrast` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;focus_border_high_contrast&quot;>#FF0099CC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\values\colors.xml"
            line="14"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.color.focus_border_medium` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="    &lt;color name=&quot;focus_border_medium&quot;>#FF33CCFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\values\colors.xml"
            line="17"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        severity="Warning"
        message="The resource `R.drawable.focus_border_high_contrast` appears to be unused"
        category="Performance"
        priority="3"
        summary="Unused resources"
        explanation="Unused resources make applications larger and slow down builds.&#xA;&#xA;&#xA;The unused resource check can ignore tests. If you want to include resources that are only referenced from tests, consider packaging them in a test source set instead.&#xA;&#xA;You can include test sources in the unused resource check by setting the system property lint.unused-resources.include-tests =true, and to exclude them (usually for performance reasons), use lint.unused-resources.exclude-tests =true.&#xA;,"
        errorLine1="&lt;layer-list xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\drawable\focus_border_high_contrast.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="MissingApplicationIcon"
        severity="Warning"
        message="Should explicitly set `android:icon`, there is no default"
        category="Usability:Icons"
        priority="5"
        summary="Missing application icon"
        explanation="You should set an icon for the application as whole because there is no default. This attribute must be set as a reference to a drawable resource containing the image (for example `@drawable/icon`)."
        url="https://developer.android.com/studio/publish/preparing#publishing-configure"
        urls="https://developer.android.com/studio/publish/preparing#publishing-configure"
        errorLine1="    &lt;application"
        errorLine2="     ~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\AndroidManifest.xml"
            line="11"
            column="6"/>
    </issue>

    <issue
        id="ContentDescription"
        severity="Warning"
        message="Missing `contentDescription` attribute on image"
        category="Accessibility"
        priority="3"
        summary="Image without `contentDescription`"
        explanation="Non-textual widgets like ImageViews and ImageButtons should use the `contentDescription` attribute to specify a textual description of the widget such that screen readers and other accessibility tools can adequately describe the user interface.&#xA;&#xA;Note that elements in application screens that are purely decorative and do not provide any content or enable a user action should not have accessibility content descriptions. In this case, set their descriptions to `@null`. If your app&apos;s minSdkVersion is 16 or higher, you can instead set these graphical elements&apos; `android:importantForAccessibility` attributes to `no`.&#xA;&#xA;Note that for text fields, you should not set both the `hint` and the `contentDescription` attributes since the hint will never be shown. Just set the `hint`."
        url="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        urls="https://developer.android.com/guide/topics/ui/accessibility/apps#special-cases"
        errorLine1="  &lt;ImageView"
        errorLine2="   ~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\layout\player_tile.xml"
            line="33"
            column="4"/>
    </issue>

    <issue
        id="HardcodedText"
        severity="Warning"
        message="Hardcoded string &quot;Cam&quot;, should use `@string` resource"
        category="Internationalization"
        priority="5"
        summary="Hardcoded text"
        explanation="Hardcoding text attributes directly in layout files is bad for several reasons:&#xA;&#xA;* When creating configuration variations (for example for landscape or portrait) you have to repeat the actual text (and keep it up to date when making changes)&#xA;&#xA;* The application cannot be translated to other languages by just adding new translations for existing string resources.&#xA;&#xA;There are quickfixes to automatically extract this hardcoded string into a resource lookup."
        errorLine1="   android:text=&quot;Cam&quot; />"
        errorLine2="   ~~~~~~~~~~~~~~~~~~">
        <location
            file="C:\Users\<USER>\StudioProjects\link-eye\app\src\main\res\layout\player_tile.xml"
            line="32"
            column="4"/>
    </issue>

</issues>
